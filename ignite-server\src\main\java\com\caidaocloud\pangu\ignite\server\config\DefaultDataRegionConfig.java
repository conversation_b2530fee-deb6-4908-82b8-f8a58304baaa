package com.caidaocloud.pangu.ignite.server.config;

import lombok.Data;
import org.apache.ignite.configuration.DataRegionConfiguration;
import org.apache.ignite.configuration.DataStorageConfiguration;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.springframework.boot.autoconfigure.IgniteConfigurer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2025/7/1
 */
// @Component
@ConfigurationProperties(prefix = "ignite.data.region.default")
@Data
@Deprecated
public class DefaultDataRegionConfig implements IgniteConfigurer {
	private long maxSize = 3221225472L; // 默认3GB
	private String name = "Default_Region"; // 默认区域名称
	private boolean persistenceEnabled = false; // 默认不启用持久化

	@Override
	public void accept(IgniteConfiguration configuration) {
		// 配置 DataStorage
		DataStorageConfiguration storageCfg = new DataStorageConfiguration();

		// 配置默认 DataRegion
		DataRegionConfiguration defaultRegion = new DataRegionConfiguration();
		defaultRegion.setName(name);
		defaultRegion.setMaxSize(maxSize);
		defaultRegion.setPersistenceEnabled(persistenceEnabled); // 根据需要配置是否持久化

		storageCfg.setDefaultDataRegionConfiguration(defaultRegion);
		configuration.setDataStorageConfiguration(storageCfg);
	}
}

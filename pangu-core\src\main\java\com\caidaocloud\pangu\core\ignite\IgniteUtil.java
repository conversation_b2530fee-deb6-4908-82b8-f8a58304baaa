package com.caidaocloud.pangu.core.ignite;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ComponentPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.pangu.core.feign.PaasMetadataFeign;
import com.caidaocloud.pangu.core.ignite.cache.CacheConfigurationBuilder;
import com.caidaocloud.pangu.core.ignite.cache.DynamicPojoCacheCfgFactory;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoContext;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import com.caidaocloud.pangu.core.ignite.store.CustomBlobStoreFactory;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.configuration.CacheConfiguration;
import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/1/4
 */
@Component
@Slf4j
public class IgniteUtil {

	private Ignite ignite;

	private DatasourceProperties datasourceProperties;
	public IgniteUtil(Ignite ignite, DatasourceProperties datasourceProperties) {
		this.ignite = ignite;
		this.datasourceProperties = datasourceProperties;
		log.info("[Ignite util] inited,db property={}", datasourceProperties);
	}

	@SneakyThrows
	public <K,V> IgniteCacheContext<K,V> keyValueCache(String cacheName, Class<? extends  K> keyType, Class<? extends V> valueType) {
		DynamicPojoDefinition pojoDefinition = initKvPojoDefinition(cacheName, keyType, valueType);
		CacheConfiguration configuration = new CacheConfigurationBuilder<>(valueType).cacheName(cacheName)
				.key(valueType.getDeclaredField("id"))
				.datasourceProperties(datasourceProperties)
				.dynamicPojoDefinition(pojoDefinition)
				.build();
		configuration.setReadThrough(true);
		configuration.setWriteThrough(true);
		IgniteCache cache = ignite.getOrCreateCache(configuration);
		dynamicDefCache().put(cacheName, pojoDefinition);
		IgniteCacheContext igniteCacheContext = new IgniteCacheContext(ignite, cache, pojoDefinition);
		return igniteCacheContext;
	}

	@SneakyThrows
	public <K,V> IgniteCacheContext<K,V> keyValueCache(String cacheName, Class<? extends  K> keyType, Map<String, Class<?>> valueTypes) {
		DynamicPojoDefinition pojoDefinition = initDynamicPojoDefinition(cacheName, keyType, valueTypes);
		DynamicPojoCacheCfgFactory factory = new DynamicPojoCacheCfgFactory<>(null, datasourceProperties);
		factory.setDynamicPojoDefinition(pojoDefinition);
		factory.setCacheName(cacheName);
		CacheConfiguration configuration = factory.create();
		configuration.setReadThrough(true);
		configuration.setWriteThrough(true);
		IgniteCache cache = ignite.getOrCreateCache(configuration);
		dynamicDefCache().put(cacheName, pojoDefinition);
		IgniteCacheContext igniteCacheContext = new IgniteCacheContext(ignite, cache, pojoDefinition);
		return igniteCacheContext;
	}

	public IgniteCacheContext dynamicPojoCache(String id, String identifier, DataFilter dataFilter, long time) {
		DynamicPojoDefinition definition = initPaasPojoDefinition(identifier);
		DynamicPojoCacheCfgFactory factory = new DynamicPojoCacheCfgFactory<>(identifier, datasourceProperties);
		factory.setDynamicPojoDefinition(definition);
		factory.setCacheName(id);
		IgniteCache cache = ignite.getOrCreateCache(factory.create());
		dynamicDefCache().put(cache.getName(), definition);
		IgniteCacheContext igniteCacheContext = new IgniteCacheContext(ignite, cache, definition);
		igniteCacheContext.initData(dataFilter, time);
		return igniteCacheContext;
	}

	@SneakyThrows
	public  IgniteCacheContext copyCache(String id, String originCacheId, DataFilter dataFilter) {
		IgniteCacheContext exist = getContext(originCacheId);
		Class<?> clazz = DynamicPojoContext.loadClass(exist.getPojoDef());
		// TODO: 2025/3/1 暂不支持paas cache copy

		CacheConfiguration cacheConfiguration = new CacheConfigurationBuilder(clazz)
				.key(clazz.getDeclaredField(exist.getPojoDef().getKeyField().getName()))
				.cacheName(id)
				.datasourceProperties(datasourceProperties)
				.dynamicPojoDefinition(exist.getPojoDef()).build();
		cacheConfiguration.setReadThrough(true);
		cacheConfiguration.setWriteThrough(true);

		IgniteCache cache = ignite.getOrCreateCache(cacheConfiguration);
		dynamicDefCache().put(id, exist.getPojoDef());
		IgniteCacheContext newCache = new IgniteCacheContext(ignite, cache, exist.getPojoDef());
		// TODO: 2025/3/1 ignite stream和datafilter内存过滤
		List<List<KeyValueDto>> existData = exist.loadData(dataFilter);
		for (List<KeyValueDto> list : existData) {
			Map map = Maps.map();
			for (KeyValueDto data : list) {
				map.put(data.getKey(), data.getValue());
			}
			Object value = FastjsonUtil.convertObject(map, clazz);
			newCache.put(map.get(newCache.getPojoDef().getKeyField()
					.getName()), value);
		}

		return newCache;
	}

	private <K> DynamicPojoDefinition initDynamicPojoDefinition(String cacheName, Class<? extends K> keyType, Map<String, Class<?>> valueTypes) {
		if (!isValidClassName(cacheName)) {
			throw new IllegalArgumentException(String.format("类名不合法，name:'%s'", cacheName));
		}
		DynamicPojoDefinition pojoDefinition = new DynamicPojoDefinition();
		pojoDefinition.setDefType(DynamicPojoDefinition.DefType.DYNAMIC);
		pojoDefinition.setClassName(cacheName + "_" + SecurityUserUtil.getSecurityUserInfo().getTenantId());

		DynamicPojoDefinition.DynamicFieldDefinition[] fields = initFieldDef(keyType, valueTypes);
		pojoDefinition.setFields(fields);
		pojoDefinition.setKeyField(fields[0]);
		return pojoDefinition;
	}

	private IgniteCache<Object, Object> dynamicDefCache() {
		return ignite.getOrCreateCache(dynamicDefCacheName());
	}

	public static String dynamicDefCacheName() {
		return "Dynamic_Def_" + SecurityUserUtil.getSecurityUserInfo().getTenantId();
	}

	private <K> DynamicPojoDefinition.DynamicFieldDefinition[] initFieldDef(Class<? extends K> keyType, Map<String, Class<?>> valueTypes) {
		List<DynamicPojoDefinition.DynamicFieldDefinition> list = new ArrayList<>();
		list.add(new DynamicPojoDefinition.DynamicFieldDefinition("id", "id", String.class));

		for (Map.Entry<String, Class<?>> entry : valueTypes.entrySet()) {
			String p = entry.getKey();
			Class<?> c = entry.getValue();
			list.add(new DynamicPojoDefinition.DynamicFieldDefinition(p, p, c));
		}
		return list.toArray(new DynamicPojoDefinition.DynamicFieldDefinition[0]);
	}

	public IgniteCache getCache(String id){
		return ignite.cache(id);
	}

	public IgniteCacheContext getContext(String id){
		IgniteCache cache = ignite.cache(id);
		if (cache == null) {
			throw new ServerException("Cache not exist,cache id :" + id);
		}
		IgniteCacheContext igniteCacheContext = new IgniteCacheContext(ignite, cache, (DynamicPojoDefinition) dynamicDefCache().get(id));
		return igniteCacheContext;
	}

	public IgniteCacheContext getContextOrNull(String id){
		IgniteCache cache = ignite.cache(id);
		if (cache == null) {
			return null;
		}
		IgniteCacheContext igniteCacheContext = new IgniteCacheContext(ignite, cache, (DynamicPojoDefinition) dynamicDefCache().get(id));
		return igniteCacheContext;
	}

	private <K,V> DynamicPojoDefinition initKvPojoDefinition(String name, Class<? extends  K> keyType, Class<? extends V> valueType) {
		DynamicPojoDefinition pojoDefinition = new DynamicPojoDefinition();
		pojoDefinition.setDefType(DynamicPojoDefinition.DefType.COMMON);
		pojoDefinition.setClassName(valueType.getName());

		List<DynamicPojoDefinition.DynamicFieldDefinition> list = Lists.list();
		for (Field field : valueType.getDeclaredFields()) {
			list.add(new DynamicPojoDefinition.DynamicFieldDefinition(field.getName(), field.getName(), field.getType()));
		}
		DynamicPojoDefinition.DynamicFieldDefinition[] fields = list.toArray(new DynamicPojoDefinition.DynamicFieldDefinition[0]);
		pojoDefinition.setFields(fields);
		pojoDefinition.setKeyField(fields[0]);

		return pojoDefinition;
	}

	public DynamicPojoDefinition initPaasPojoDefinition(String identifier) {
		Map<String, String> map = SpringUtil.getBean(PaasMetadataFeign.class)
				.getIdentifierFieldMapping(identifier, SecurityUserUtil.getThreadLocalSecurityUserInfo()
						.getTenantId()).getData();
		MetadataVo metadataVo = SpringUtil.getBean(MetadataOperatorService.class).load(identifier);
		String name = SnakeCaseConvertor.toSnake(identifier);
		if (!isValidClassName(name)) {
			throw new IllegalArgumentException(String.format("类名不合法，identifier:'%s'", metadataVo.getIdentifier()));
		}
		name = name + "_" + SecurityUserUtil.getSecurityUserInfo()
				.getTenantId();

		DynamicPojoDefinition pojoDefinition = new DynamicPojoDefinition();
		pojoDefinition.setDefType(DynamicPojoDefinition.DefType.PAAS);
		pojoDefinition.setClassName(name);
		pojoDefinition.setTableName(SnakeCaseConvertor.toSnake(identifier) + "_" + SecurityUserUtil.getSecurityUserInfo()
				.getTenantId());
		map.put("identifier", "identifier");
		map.put("bid", "bid");
		map.put("id", "id");
		map.put("tenantId", "tenant_id");
		map.put("createTime","create_time");
		map.put("createBy","create_by");
		map.put("updateTime","update_time");
		map.put("updateBy","update_by");
		map.put("dataStartTime","data_start_time");
		map.put("dataEndTime", "data_end_time");
		DynamicPojoDefinition.DynamicFieldDefinition[] fields = initFieldDef(metadataVo, map);
		pojoDefinition.setFields(fields);
		pojoDefinition.setKeyField(new DynamicPojoDefinition.DynamicFieldDefinition("bid", "bid", String.class));

		return pojoDefinition;
	}

	private DynamicPojoDefinition.DynamicFieldDefinition[] initFieldDef(MetadataVo metadataVo, Map<String, String> map) {
		List<DynamicPojoDefinition.DynamicFieldDefinition> list = new ArrayList<>();
		// list.add(new DynamicPojoDefinition.DynamicFieldDefinition("bid", "bid", String.class));

		for (MetadataPropertyVo property : metadataVo.fetchAllProperties()) {
			String p = property.getProperty();
			PropertyDataType t = property.getDataType();
			if (property.isExpEnable()) {
				continue;
			}
			if (t.isComponent()) {
				property.getDataType().propertySuffixToWhetherLong().forEach((suffix, isLong) -> {
					String dbp = p + suffix;
					String p_ = dbp.replace(".", "$").substring(0, 1).toLowerCase() + dbp.replace(".", "$").substring(1);
					list.add(new DynamicPojoDefinition.PaasDynamicFieldDefinition(p_, map.get(dbp), String.class,dbp.replace(".", "$")));
						});
			}else {
				String p_ = property.getProperty().substring(0, 1).toLowerCase() + property.getProperty().substring(1);
				list.add(new DynamicPojoDefinition.PaasDynamicFieldDefinition(p_, map.getOrDefault(p, p), t == PropertyDataType.Number? BigDecimal.class : t == PropertyDataType.Integer ? Long.class : String.class, p));
			}
		}
		return list.toArray(new DynamicPojoDefinition.DynamicFieldDefinition[0]);
	}

	/**
	 * 校验类名是否合法
	 * @param className 待校验的类名
	 * @return true 如果类名合法，否则返回 false
	 */
	public static boolean isValidClassName(String className) {
		if (className == null || className.isEmpty()) {
			return false;
		}

		String regex = "^[a-zA-Z_$][a-zA-Z\\d_$]*$";

		// 检查类名的基础合法性
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(className);

		if (!matcher.matches()) {
			return false;
		}

		return true;
	}


}


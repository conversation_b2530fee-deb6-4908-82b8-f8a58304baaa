package com.caidaocloud.pangu.core.ignite.config;

import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import org.apache.ignite.configuration.DataRegionConfiguration;
import org.apache.ignite.configuration.DataStorageConfiguration;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi;
import org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder;
import org.apache.ignite.springframework.boot.autoconfigure.IgniteConfigurer;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 整合的Ignite配置器，包含静态IP发现和默认数据区域配置
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Component
@ConfigurationProperties(prefix = "caidaocloud.ignite")
@Data
public class IntegratedIgniteConfigurer implements IgniteConfigurer {

	/**
	 * 静态IP配置
	 */
	private StaticIpConfig server = new StaticIpConfig();

	/**
	 * 数据区域配置
	 */
	private DataRegionConfig dataRegion = new DataRegionConfig();

	@Override
	public void accept(IgniteConfiguration configuration) {
		// 配置静态IP发现
		configureStaticIpDiscovery(configuration);

		// 配置默认数据区域
		configureDefaultDataRegion(configuration);
	}

	/**
	 * 配置静态IP发现
	 */
	private void configureStaticIpDiscovery(IgniteConfiguration configuration) {
		if (server != null && !server.getIpConfigs().isEmpty()) {
			// 创建 TcpDiscoverySpi 实例
			TcpDiscoverySpi spi = new TcpDiscoverySpi();
			// 配置 IP 查找器（使用配置文件中的 IP 列表）
			TcpDiscoveryVmIpFinder ipFinder = new TcpDiscoveryVmIpFinder();

			// 从配置中获取 IP 地址和端口范围
			List<String> addresses = server.getIpConfigs().stream()
					.map(ipConfig -> ipConfig.getIp() + ":" + ipConfig.getBasePort() + ".." + ipConfig.getEndPort())
					.collect(Collectors.toList());

			ipFinder.setAddresses(addresses);
			spi.setIpFinder(ipFinder);

			// 将配置好的 SPI 设置到 IgniteConfiguration
			configuration.setDiscoverySpi(spi);
		}
	}

	/**
	 * 配置默认数据区域
	 */
	private void configureDefaultDataRegion(IgniteConfiguration configuration) {
		if (dataRegion != null) {
			// 配置 DataStorage
			DataStorageConfiguration storageCfg = configuration.getDataStorageConfiguration();
			if (storageCfg == null) {
				storageCfg = new DataStorageConfiguration();
			}

			// 配置默认 DataRegion
			DataRegionConfiguration defaultRegion = new DataRegionConfiguration();
			defaultRegion.setName(dataRegion.getName());
			defaultRegion.setMaxSize(dataRegion.getMaxSize());
			defaultRegion.setPersistenceEnabled(dataRegion.isPersistenceEnabled());

			storageCfg.setDefaultDataRegionConfiguration(defaultRegion);
			configuration.setDataStorageConfiguration(storageCfg);
		}
	}

	/**
	 * 数据区域配置内部类
	 */
	@Data
	public static class DataRegionConfig {
		private long maxSize = 3221225472L; // 默认3GB
		private String name = "Default_Region"; // 默认区域名称
		private boolean persistenceEnabled = false; // 默认不启用持久化
	}
}

package com.caidaocloud.pangu.core.ignite;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.pangu.core.dto.IgniteCacheValue;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoContext;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import com.caidaocloud.pangu.core.ignite.store.CustomStoreFactory;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.cache.query.QueryCursor;
import org.apache.ignite.cache.query.ScanQuery;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.internal.binary.BinaryObjectImpl;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/1/13
 */
@Slf4j
@Data
public class IgniteCacheContext<K,V> implements AutoCloseable {
	private final Ignite ignite;

	@Getter
	private final IgniteCache<K, V> cache;

	private final DynamicPojoDefinition pojoDef;
	private final CacheSqlParser cacheSqlParser;

	public IgniteCacheContext(Ignite ignite, IgniteCache cache, DynamicPojoDefinition pojoDefinition) {
		this.ignite = ignite;
		this.cache = cache;
		this.pojoDef = pojoDefinition;
		this.cacheSqlParser = new CacheSqlParser(pojoDef);
	}

	@Override
	public void close() throws Exception {
		cache.close();
	}

	public void initData(DataFilter dataFilter, long time) {
		if (dataFilter == null) {
			log.info("load data without filter,table name:{}", pojoDef.getTableName());
		}
		String sql = cacheSqlParser.loadCacheSql(dataFilter, time);
		cache.loadCache(null, pojoDef.getKeyField().getType().getName(), sql);
	}

	public void destroy(){
		ignite.destroyCache(cache.getName());
		ignite.getOrCreateCache(IgniteUtil.dynamicDefCacheName()).remove(cache.getName());
	}

	public void put(K k, V v){
		cache.put(k, v);
	}

	public void putAll(Map<K, V> map) {
		cache.putAll(map);
	}

	public V get(K k){
		BinaryObjectImpl binaryImpl = ((BinaryObjectImpl) cache.withKeepBinary().get(k));
		Class clazz = DynamicPojoContext.loadClass(pojoDef);
		return binaryImpl.deserialize(clazz.getClassLoader());
	}

	public List<List<KeyValueDto>> loadProperties(DataFilter dataFilter, List<String> properties, List<String> orderBys, int from, int limit) {
		if (properties == null || properties.isEmpty()) {
			properties = pojoDef.fieldNames();
		}
		String sql = cacheSqlParser.loadPropertySql(dataFilter, properties, orderBys, from, limit);

		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		List<List<?>> ret = cache.query(query).getAll();
		List<String> finalProperties =properties;
		return Sequences.sequence(ret).map(row -> {
			List<KeyValueDto> list = Lists.list();
			list.add(new KeyValueDto(pojoDef.getKeyField().getName(), row.get(0)));

			for (int i = 0; i < finalProperties.size(); i++) {
				list.add(new KeyValueDto(finalProperties.get(i), row.get(i + 1)));
			}
			return list;
		}).toList();
	}

	public  List<List<KeyValueDto>> loadData(DataFilter dataFilter) {
		return loadProperties(dataFilter, null, null, 0, 0);
	}

	public double sum(DataFilter dataFilter, String property) {
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("sum(%s)", s));
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		List<List<?>> ret = cache.query(query).getAll();
		// TODO: 2025/3/17 double or bigdecimal
		return ret.isEmpty() ? 0 : Double.parseDouble(ret.get(0).get(0).toString());
	}

	public QueryCursor<List<?>> sum(DataFilter dataFilter, String property,String... groupBy) {
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("sum(%s)", s),groupBy);
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		return cache.query(query);
	}

	public Object max(DataFilter dataFilter, String property){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("max(%s)", s));
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		List<List<?>> ret = cache.query(query).getAll();
		return ret.isEmpty() ? null : ret.get(0).get(0);
	}

	public QueryCursor<List<?>> max(DataFilter dataFilter, String property,String... groupBy){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("max(%s)", s),groupBy);
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		return cache.query(query);
	}

	public Object min(DataFilter dataFilter, String property){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("min(%s)", s));
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		List<List<?>> ret = cache.query(query).getAll();
		return ret.isEmpty() ? null : ret.get(0).get(0);
	}
	public QueryCursor<List<?>> min(DataFilter dataFilter, String property,String... groupBy){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("min(%s)", s),groupBy);
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		return cache.query(query);
	}

	public long count(DataFilter dataFilter, String property){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("count(%s)", s));
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		List<List<?>> ret = cache.query(query).getAll();
		return ret.isEmpty() ? 0 : (Long) ret.get(0).get(0);
	}

	public QueryCursor<List<?>> count(DataFilter dataFilter, String property,String... groupBy){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("count(%s)", s),groupBy);
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		return cache.query(query);
	}

	public double avg(DataFilter dataFilter, String property){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("avg(%s)", s));
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		List<List<?>> ret = cache.query(query).getAll();
		return ret.isEmpty() ? 0 : (Double) ret.get(0).get(0);
	}
	public QueryCursor<List<?>> avg(DataFilter dataFilter, String property, String... groupBy){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("avg(%s)", s),groupBy);
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		return cache.query(query);
	}

	public QueryCursor<List<?>> avg(DataFilter dataFilter, String property, int precision, RoundingMode roundingMode, String... groupBy){
		String sql = cacheSqlParser.functionSql(dataFilter, property, s -> String.format("count(%s),sum(%s)", s, s), groupBy);
		SqlFieldsQuery query = new SqlFieldsQuery(sql);
		return cache.query(query);
	}

	@SneakyThrows
	public void putValue(K key, List<IgniteCacheValue> values) {
		Class<?> clazz = DynamicPojoContext.loadClass(pojoDef.getClassName());
		
		Object obj = clazz.getDeclaredConstructor().newInstance();
		
		for (IgniteCacheValue cacheValue : values) {
			Field field = clazz.getDeclaredField(cacheValue.getProperty());
			field.setAccessible(true);
			field.set(obj, cacheValue.getValue());
		}
		cache.put(key, (V) obj);
	}


}

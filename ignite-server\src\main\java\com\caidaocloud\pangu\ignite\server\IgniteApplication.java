package com.caidaocloud.pangu.ignite.server;

import javax.annotation.PostConstruct;

import com.caidaocloud.pangu.core.ignite.config.IntegratedIgniteConfigurer;
import org.apache.ignite.Ignite;
import org.apache.ignite.cluster.ClusterState;
import org.apache.ignite.configuration.DataRegionConfiguration;
import org.apache.ignite.springframework.boot.autoconfigure.IgniteConfigurer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

/**
 *
 * <AUTHOR>
 * @date 2025/3/3
 */
@SpringBootApplication
public class IgniteApplication {
	// @Autowired
	// private Ignite ignite;

	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(IgniteApplication.class, args);
		Ignite ignite = context.getBean(Ignite.class);
		DataRegionConfiguration region = context.getBean(Ignite.class).configuration().getDataStorageConfiguration()
				.getDefaultDataRegionConfiguration();
		System.out.println("Data Region Name: " + region.getName());
		System.out.println("Max Size: " + region.getMaxSize());
		System.out.println("Persistence Enabled: " + region.isPersistenceEnabled());

		ignite.cluster().state(ClusterState.ACTIVE);
	}

	@Bean
	public IgniteConfigurer igniteConfigurer(){
		return new IntegratedIgniteConfigurer();
	}
}

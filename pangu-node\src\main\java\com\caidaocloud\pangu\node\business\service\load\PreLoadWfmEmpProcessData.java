package com.caidaocloud.pangu.node.business.service.load;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataTruncate;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import com.caidaocloud.pangu.node.business.feign.AttendanceWfmFeign;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class PreLoadWfmEmpProcessData implements PreLoadDataInterface{

    private String identifier = "entity.pangu.WfmEmpProcessData";

    @Autowired
    private AttendanceWfmFeign attendanceWfmFeign;
    @Override
    public KeyValue textToValue() {
        return new KeyValue("员工工序产出", "WFM_EMP_PROCESS");
    }

    @Override
    public void exec(Map<String, String> context) {
        int pageNo = 1;
        val accountId = context.get("$.env."+ EnvironmentContext.BONUS_ACCOUNT_ID);
        //DataTruncate.identifier(identifier).truncate();
        DataDelete.identifier(identifier).batchDelete(DataFilter.eq("accountId", accountId));
        while(true){
            log.info("PreLoadWfmEmpProcessData call " +
                    Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_START)) + ","
                    +Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_END)) + ","+pageNo+","+accountId);

            val page = attendanceWfmFeign
                    .getEmpArrangeMonthAnalyzeList(
                            Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_START)),
                            Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_END)),
                            pageNo, 100,
                            accountId
                            ).getData();
            pageNo++;
            if(page.getItems().isEmpty()){
                break;
            }
            page.getItems().forEach(it-> {
                it.setIdentifier(identifier);
                it.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            });
            DataInsert.identifier(identifier).batchInsert(page.getItems());
            if(page.getPageNo() * page.getPageSize() >= page.getTotal()){
                break;
            }
        }
        val pageNonProcess = attendanceWfmFeign
                .getEmpNonProcessArrangeMonthAnalyzeList(
                        Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_START)),
                        Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_END)),
                        1, -1,
                        accountId
                ).getData();
        if(pageNonProcess.getItems().isEmpty()){
            return;
        }
        pageNonProcess.getItems().forEach(it-> {
            it.setIdentifier(identifier);
            it.setDataEndTime(DateUtil.MAX_TIMESTAMP);
        });
        DataInsert.identifier(identifier).batchInsert(pageNonProcess.getItems());

    }
}
